import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from './$types';
import { insertBestLine, getBestLine } from '$lib/db';
import { solveDailyBoard } from '$lib/solver';
import type { BestLineResult } from '$lib/types';

/**
 * Webhook endpoint for triggering the daily solver
 *
 * This endpoint is designed to be called by external automation (e.g., cron jobs)
 * to run the solver once per day. It uses Cloudflare Browser Rendering to solve
 * the daily Letters game puzzle and stores the result in D1.
 */

/**
 * Verify webhook authentication
 */
function verifyAuthentication(request: Request): boolean {
	const authHeader = request.headers.get('Authorization');
	const webhookSecret = process.env.WEBHOOK_SECRET || 'dev-secret-key';

	if (!authHeader) {
		return false;
	}

	// Support both "Bearer token" and "token" formats
	const token = authHeader.startsWith('Bearer ') ? authHeader.slice(7) : authHeader;

	return token === webhookSecret;
}

/**
 * Validate request body for the webhook
 */
function validateRequest(body: any): { date?: string; force?: boolean } {
	// Allow empty body for default behavior
	if (!body) {
		return {};
	}

	const result: { date?: string; force?: boolean } = {};

	// Validate date format if provided
	if (body.date) {
		if (typeof body.date !== 'string' || !/^\d{4}-\d{2}-\d{2}$/.test(body.date)) {
			throw new Error('Invalid date format. Expected YYYY-MM-DD');
		}
		result.date = body.date;
	}

	// Validate force flag if provided
	if (body.force !== undefined) {
		if (typeof body.force !== 'boolean') {
			throw new Error('Force flag must be a boolean');
		}
		result.force = body.force;
	}

	return result;
}

/**
 * Get today's date in YYYY-MM-DD format
 */
function getTodayDate(): string {
	return new Date().toISOString().split('T')[0];
}

/**
 * POST /api/run - Trigger the daily solver
 *
 * Request body (optional):
 * {
 *   "date": "2025-06-17",  // Optional: specific date to solve (defaults to today)
 *   "force": false         // Optional: force re-solve even if result exists
 * }
 *
 * Returns HTTP 202 and starts a Cloudflare Browser Rendering job
 */
export const POST: RequestHandler = async ({ request, platform }) => {
	try {
		// Verify authentication
		if (!verifyAuthentication(request)) {
			return new Response(
				JSON.stringify({
					success: false,
					error: 'Unauthorized',
					message: 'Invalid or missing authentication token'
				}),
				{
					status: 401,
					headers: { 'Content-Type': 'application/json' }
				}
			);
		}

		// Check if we have access to required services
		if (!platform?.env?.DB) {
			return new Response(
				JSON.stringify({
					success: false,
					error: 'Service Unavailable',
					message: 'D1 database not available'
				}),
				{
					status: 503,
					headers: { 'Content-Type': 'application/json' }
				}
			);
		}

		if (!platform?.env?.BROWSER) {
			return new Response(
				JSON.stringify({
					success: false,
					error: 'Service Unavailable',
					message: 'Browser Rendering service not available'
				}),
				{
					status: 503,
					headers: { 'Content-Type': 'application/json' }
				}
			);
		}

		// Parse and validate request body
		let requestData: { date?: string; force?: boolean } = {};
		try {
			const body = await request.json().catch(() => ({}));
			requestData = validateRequest(body);
		} catch (validationError) {
			return new Response(
				JSON.stringify({
					success: false,
					error: 'Bad Request',
					message:
						validationError instanceof Error ? validationError.message : 'Invalid request body'
				}),
				{
					status: 400,
					headers: { 'Content-Type': 'application/json' }
				}
			);
		}

		const targetDate = requestData.date || getTodayDate();
		const force = requestData.force || false;

		// Check if we already have a result for this date (unless force is true)
		if (!force) {
			try {
				const existingResult = await getBestLine(platform.env.DB, targetDate);
				if (existingResult) {
					return new Response(
						JSON.stringify({
							success: true,
							message: 'Result already exists for this date',
							data: {
								date: targetDate,
								alreadyExists: true,
								result: existingResult
							}
						}),
						{
							status: 200,
							headers: { 'Content-Type': 'application/json' }
						}
					);
				}
			} catch (dbError) {
				console.warn(`Failed to check existing result for ${targetDate}:`, dbError);
				// Continue with solving - better to have duplicate than miss a day
			}
		}

		// Start the solver job asynchronously
		// Note: In a real implementation, this would be handled by Cloudflare's
		// Browser Rendering service in a separate worker context
		const solverPromise = runSolverJob(platform.env.BROWSER, platform.env.DB, targetDate);

		// Return HTTP 202 immediately to indicate job started
		return new Response(
			JSON.stringify({
				success: true,
				message: 'Solver job started',
				data: {
					date: targetDate,
					jobStarted: true,
					timestamp: new Date().toISOString()
				}
			}),
			{
				status: 202,
				headers: { 'Content-Type': 'application/json' }
			}
		);
	} catch (error) {
		console.error('Webhook error:', error);

		return new Response(
			JSON.stringify({
				success: false,
				error: 'Internal Server Error',
				message: error instanceof Error ? error.message : 'Unknown error occurred',
				timestamp: new Date().toISOString()
			}),
			{
				status: 500,
				headers: { 'Content-Type': 'application/json' }
			}
		);
	}
};

/**
 * Run the solver job with Browser Rendering
 * This function runs asynchronously after the webhook returns
 */
async function runSolverJob(browser: any, db: any, date: string): Promise<void> {
	try {
		console.log(`[runSolverJob] Starting solver for date: ${date}`);

		// Launch browser session
		const session = await browser.launch();
		const page = await session.newPage();

		try {
			// Navigate to the Letters game
			await page.goto('https://play.thelettersgame.com/');

			// Run the solver
			const result: BestLineResult = await solveDailyBoard(page);

			// Store the result in the database
			await insertBestLine(db, date, result);

			console.log(`[runSolverJob] Successfully solved and stored result for ${date}:`, {
				total: result.total,
				words: result.words,
				perRoundCount: result.perRound.length
			});
		} finally {
			// Always close the browser session
			await session.close();
		}
	} catch (error) {
		console.error(`[runSolverJob] Failed to solve for date ${date}:`, error);
		// In a production system, you might want to store error information
		// or trigger alerts for failed solver runs
		throw error;
	}
}

/**
 * GET /api/run - Get status information about the solver service
 * This is mainly for health checks and debugging
 */
export const GET: RequestHandler = async ({ platform }) => {
	try {
		const status = {
			service: 'LettersBot Solver Webhook',
			version: '1.0.0',
			timestamp: new Date().toISOString(),
			services: {
				database: !!platform?.env?.DB,
				browser: !!platform?.env?.BROWSER
			}
		};

		return new Response(
			JSON.stringify({
				success: true,
				data: status
			}),
			{
				status: 200,
				headers: { 'Content-Type': 'application/json' }
			}
		);
	} catch (error) {
		return new Response(
			JSON.stringify({
				success: false,
				error: 'Internal Server Error',
				message: error instanceof Error ? error.message : 'Unknown error'
			}),
			{
				status: 500,
				headers: { 'Content-Type': 'application/json' }
			}
		);
	}
};
