# Cloudflare Worker configuration for LettersBot Scheduler
# This is a separate worker that handles the cron triggers and calls the main application

name = "letters-bot-scheduler"
main = "worker.ts"
compatibility_date = "2024-12-01"
compatibility_flags = ["nodejs_compat"]

# Cron triggers for scheduled tasks
[triggers]
crons = ["0 6 * * *"]  # Run daily at 6 AM UTC

# Environment variables for development
[env.development.vars]
NODE_ENV = "development"
LETTERS_BOT_DOMAIN = "http://localhost:8788"  # Local development URL
# WEBHOOK_SECRET = "your-webhook-secret-here"  # Set this as a secret

# Environment variables for production
[env.production.vars]
NODE_ENV = "production"
LETTERS_BOT_DOMAIN = "https://letters-bot.pages.dev"  # Replace with your actual domain
# WEBHOOK_SECRET = "your-webhook-secret-here"  # Set this as a secret

# Secrets (set these using wrangler secret put)
# wrangler secret put WEBHOOK_SECRET --env development
# wrangler secret put WEBHOOK_SECRET --env production
