# Cloudflare Workers configuration for LettersBot
name = "letters-bot"
compatibility_date = "2024-12-01"
compatibility_flags = ["nodejs_compat"]

# Pages configuration
pages_build_output_dir = "build"

# Environment variables
[vars]
# Add any public environment variables here

# D1 Database bindings for CLI access (uses production database)
[[d1_databases]]
binding = "DB"
database_name = "letters-bot-db-prod"
database_id = "2a3cee2e-eb47-4427-acb8-76ac7dd153cc"

# Browser Rendering binding
[browser]
binding = "BROWSER"

# Preview environment - uses local D1 database for development
[env.preview]
vars = { NODE_ENV = "preview" }

# Local D1 database for preview/development
[[env.preview.d1_databases]]
binding = "DB"
database_name = "letters-bot-db-local"
database_id = "48025a9c-422f-4980-90ac-dd094ac3aab6"

# Production environment
[env.production]
vars = { NODE_ENV = "production" }

[[env.production.d1_databases]]
binding = "DB"
database_name = "letters-bot-db-prod"
database_id = "2a3cee2e-eb47-4427-acb8-76ac7dd153cc"
