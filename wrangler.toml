# Cloudflare Workers configuration for LettersBot
name = "letters-bot"
compatibility_date = "2024-12-01"
compatibility_flags = ["nodejs_compat"]

# Pages configuration
pages_build_output_dir = "build"

# Environment variables
[vars]
# Add any public environment variables here

# D1 Database bindings for development (main section for CLI access)
[[d1_databases]]
binding = "DB"
database_name = "letters-bot-db-local"
database_id = "48025a9c-422f-4980-90ac-dd094ac3aab6"

# Browser Rendering binding
[browser]
binding = "BROWSER"

# Development environment - uses local D1 database
[env.development]
vars = { NODE_ENV = "development" }

# Local D1 database for development
[[env.development.d1_databases]]
binding = "DB"
database_name = "letters-bot-db-local"
database_id = "48025a9c-422f-4980-90ac-dd094ac3aab6"

# Production environment
[env.production]
vars = { NODE_ENV = "production" }

[[env.production.d1_databases]]
binding = "DB"
database_name = "letters-bot-db-prod"
database_id = "placeholder-prod-database-id"  # Will be replaced with actual ID when deployed
