#!/bin/bash

# LettersBot Deployment Script
# This script automates the deployment process to Cloudflare Pages

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="letters-bot"
ENVIRONMENT="production"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if wrangler is installed
    if ! command -v wrangler &> /dev/null; then
        log_error "Wrangler CLI is not installed. Install with: npm install -g wrangler"
        exit 1
    fi
    
    # Check if logged in to Cloudflare
    if ! wrangler whoami &> /dev/null; then
        log_error "Not logged in to Cloudflare. Run: wrangler login"
        exit 1
    fi
    
    # Check if Node.js version is compatible
    NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        log_error "Node.js version 18+ required. Current version: $(node --version)"
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

setup_database() {
    log_info "Setting up production database..."
    
    # Check if database already exists
    if wrangler d1 list | grep -q "letters-bot-db-prod"; then
        log_warning "Production database already exists"
    else
        log_info "Creating production database..."
        wrangler d1 create letters-bot-db-prod
        log_warning "Please update wrangler.toml with the database ID shown above"
        read -p "Press Enter after updating wrangler.toml..."
    fi
    
    # Run migrations
    log_info "Running database migrations..."
    wrangler d1 migrations apply DB --env production
    
    # Verify database setup
    log_info "Verifying database setup..."
    TABLES=$(wrangler d1 execute DB --env production --command "SELECT name FROM sqlite_master WHERE type='table';" --json)
    if echo "$TABLES" | grep -q "best_lines"; then
        log_success "Database setup completed successfully"
    else
        log_error "Database setup failed - best_lines table not found"
        exit 1
    fi
}

check_environment_variables() {
    log_info "Checking environment variables..."
    
    # Check if WEBHOOK_SECRET is set
    if ! wrangler pages secret list --project-name "$PROJECT_NAME" 2>/dev/null | grep -q "WEBHOOK_SECRET"; then
        log_warning "WEBHOOK_SECRET not set"
        read -p "Enter webhook secret (or press Enter to generate one): " WEBHOOK_SECRET
        
        if [ -z "$WEBHOOK_SECRET" ]; then
            WEBHOOK_SECRET=$(openssl rand -hex 32)
            log_info "Generated webhook secret: $WEBHOOK_SECRET"
        fi
        
        echo "$WEBHOOK_SECRET" | wrangler pages secret put WEBHOOK_SECRET --project-name "$PROJECT_NAME"
        log_success "WEBHOOK_SECRET set"
    else
        log_success "WEBHOOK_SECRET already configured"
    fi
}

build_application() {
    log_info "Building application..."
    
    # Install dependencies
    npm ci
    
    # Run tests
    log_info "Running tests..."
    npm run test:unit -- --run
    
    # Build application
    npm run build
    
    if [ -d "build" ]; then
        log_success "Application built successfully"
    else
        log_error "Build failed - build directory not found"
        exit 1
    fi
}

deploy_to_pages() {
    log_info "Deploying to Cloudflare Pages..."
    
    # Deploy to Pages
    wrangler pages deploy build --project-name "$PROJECT_NAME" --env "$ENVIRONMENT"
    
    log_success "Deployment completed"
}

verify_deployment() {
    log_info "Verifying deployment..."
    
    # Get the deployment URL
    DEPLOYMENT_URL=$(wrangler pages project list | grep "$PROJECT_NAME" | awk '{print $3}' | head -1)
    
    if [ -z "$DEPLOYMENT_URL" ]; then
        log_warning "Could not determine deployment URL automatically"
        read -p "Enter your deployment URL: " DEPLOYMENT_URL
    fi
    
    log_info "Testing deployment at: $DEPLOYMENT_URL"
    
    # Test home page
    if curl -s -o /dev/null -w "%{http_code}" "$DEPLOYMENT_URL" | grep -q "200"; then
        log_success "Home page is accessible"
    else
        log_error "Home page is not accessible"
    fi
    
    # Test API endpoints
    API_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$DEPLOYMENT_URL/api/run")
    if [ "$API_STATUS" = "200" ] || [ "$API_STATUS" = "401" ]; then
        log_success "API endpoint is responding"
    else
        log_error "API endpoint is not responding correctly (status: $API_STATUS)"
    fi
    
    log_info "Deployment URL: $DEPLOYMENT_URL"
}

cleanup() {
    log_info "Cleaning up temporary files..."
    # Add any cleanup tasks here
    log_success "Cleanup completed"
}

main() {
    echo "🚀 LettersBot Deployment Script"
    echo "================================"
    echo
    
    check_prerequisites
    echo
    
    setup_database
    echo
    
    check_environment_variables
    echo
    
    build_application
    echo
    
    deploy_to_pages
    echo
    
    verify_deployment
    echo
    
    cleanup
    echo
    
    log_success "🎉 Deployment completed successfully!"
    echo
    echo "Next steps:"
    echo "1. Set up custom domain (optional)"
    echo "2. Configure monitoring and alerts"
    echo "3. Set up automated solver runs"
    echo "4. Test the webhook endpoint with your automation"
    echo
}

# Handle script interruption
trap 'log_error "Deployment interrupted"; exit 1' INT TERM

# Run main function
main "$@"
